import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import json
import schedule
import time
import threading
import webbrowser
from PIL import Image, ImageDraw
import pystray
import winsound
import os
from win32gui import *
from win32con import *
from win32api import *
import win32con
import win32gui
import win32api

class TaskManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.withdraw()  # 先隐藏窗口，等初始化完成后再显示
        self.root.title('短线热点追踪复盘任务管理系统')
        self.root.geometry('800x600')
        
        # 任务数据
        self.tasks_file = 'tasks.json'
        self.tasks = self.load_tasks()
        
        # 设置主题颜色和样式
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # 配置颜色方案
        self.primary_color = '#2196F3'  # 主色调
        self.secondary_color = '#64B5F6'  # 次要色调
        self.bg_color = '#F5F5F5'  # 背景色
        self.text_color = '#333333'  # 文字颜色
        
        # 配置全局字体
        self.default_font = ('Microsoft YaHei UI', 10)
        self.title_font = ('Microsoft YaHei UI', 12, 'bold')
        
        # 设置窗口背景色
        self.root.configure(bg=self.bg_color)
        
        # 配置Treeview样式
        self.style.configure('Treeview',
                            background=self.bg_color,
                            foreground=self.text_color,
                            fieldbackground=self.bg_color,
                            font=self.default_font)
        
        self.style.configure('Treeview.Heading',
                            background=self.primary_color,
                            foreground='white',
                            font=self.title_font)
        
        # 配置按钮样式
        self.style.configure('TButton',
                            font=self.default_font,
                            background=self.primary_color,
                            foreground='white')
        
        self.style.map('TButton',
                       background=[('active', self.secondary_color)],
                       foreground=[('active', 'white')])
        
        # 初始化通知系统
        self.notification_queue = []
        
        # 设置UI
        self.setup_ui()
        
        # 初始化任务列表
        self.update_task_list()
        
        # 设置调度器
        self.setup_scheduler()
        
        # 创建系统托盘图标
        self.setup_system_tray()
        
        # 启动调度器线程
        self.scheduler_thread = threading.Thread(target=self.run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        # 显示窗口并确保在前台
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()
    def load_tasks(self):
        if os.path.exists(self.tasks_file):
            with open(self.tasks_file, 'r', encoding='utf-8') as f:
                tasks = json.load(f)
                # 确保所有任务都有is_one_time字段
                for task in tasks:
                    if 'is_one_time' not in task:
                        task['is_one_time'] = False
                return tasks
        return [
            {
                'name': '盘淘股吧账号更新',
                'url': 'https://www.tgb.cn/user/getMoreListAction',
                'time_windows': ['21:00-23:00', '07:00-09:00'],
                'completed': False,
                'is_one_time': False
            },
            {
                'name': '雪球账号分组更新',
                'url': 'https://xueqiu.com/v4/statuses/home_timeline.json?source=user&usergroup_id=16688632',
                'time_windows': ['21:00-23:00', '07:00-09:00'],
                'completed': False,
                'is_one_time': False
            },
            {
                'name': '微信公众号更新',
                'url': '',
                'time_windows': ['12:30-13:00', '17:00-17:30'],
                'completed': False,
                'is_one_time': False
            },
            {
                'name': '知识星球更新',
                'url': 'https://wx.zsxq.com/group/48848484411448',
                'time_windows': ['09:30-15:00'],
                'completed': False,
                'is_one_time': False
            },
            {
                'name': '开盘啦APP检查',
                'url': '',
                'time_windows': ['21:00-23:00', '07:00-09:00'],
                'completed': False,
                'is_one_time': False
            },
            {
                'name': '财联社头条检查',
                'url': '',
                'time_windows': ['21:00-23:00', '07:00-09:00'],
                'completed': False,
                'is_one_time': False
            },
            {
                'name': '复盘盒子APP检查',
                'url': '',
                'time_windows': ['21:00-23:00'],
                'completed': False,
                'is_one_time': False
            },
            {
                'name': '新股上市情况',
                'url': 'https://data.eastmoney.com/xg/xg/',
                'time_windows': ['21:00-23:00'],
                'completed': False,
                'is_one_time': False
            },
            {
                'name': '财经新闻监控系统',
                'url': 'http://localhost:3000',
                'time_windows': ['21:00-23:00', '07:00-09:00'],
                'completed': False,
                'is_one_time': False
            }
        ]
    def save_tasks(self):
        with open(self.tasks_file, 'w', encoding='utf-8') as f:
            json.dump(self.tasks, f, ensure_ascii=False, indent=2)
    def setup_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标题标签
        title_label = ttk.Label(main_frame, text="任务管理系统", font=('Microsoft YaHei UI', 16, 'bold'), foreground=self.primary_color)
        title_label.pack(pady=(0, 20))
        
        # 创建任务列表框架
        list_frame = ttk.Frame(main_frame, style='Card.TFrame')
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 配置Card样式
        self.style.configure('Card.TFrame', background='white')
        
        # 创建任务列表
        self.tree = ttk.Treeview(list_frame, columns=('Task', 'Status', 'Time Windows', 'Type', 'URL', 'NeedRemind'), show='headings', style='Custom.Treeview')
        self.tree.heading('Task', text='任务名称')
        self.tree.heading('Status', text='状态')
        self.tree.heading('Time Windows', text='时间窗口')
        self.tree.heading('Type', text='任务类型')
        self.tree.heading('URL', text='URL')
        self.tree.heading('NeedRemind', text='当前需轮动提醒任务')
        
        # 设置列宽
        self.tree.column('Task', width=200)
        self.tree.column('Status', width=80)
        self.tree.column('Time Windows', width=150)
        self.tree.column('Type', width=80)
        self.tree.column('URL', width=150)
        self.tree.column('NeedRemind', width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局任务列表和滚动条
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=15)
        
        # 创建按钮并添加间距
        buttons = [
            ('标记完成', self.mark_completed),
            ('重置状态', self.reset_status),
            ('手动重置', self.reset_all_tasks),
            ('添加任务', self.add_task_dialog),
            ('修改任务', self.edit_task_dialog),
            ('删除任务', self.delete_task),
            ('窗口置顶', self.toggle_topmost)
        ]
        
        for text, command in buttons:
            btn = ttk.Button(button_frame, text=text, command=command, style='TButton')
            btn.pack(side=tk.LEFT, padx=5)
        
        # 绑定双击事件
        self.tree.bind('<Double-1>', self.open_task_url)

    def toggle_topmost(self):
        # 切换窗口置顶状态
        is_topmost = self.root.attributes('-topmost')
        self.root.attributes('-topmost', not is_topmost)
    def edit_task_dialog(self):
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning('警告', '请先选择一个任务')
            return
        
        task_name = self.tree.item(selected_item[0])['values'][0]
        task = next((t for t in self.tasks if t['name'] == task_name), None)
        
        if not task:
            return
        
        dialog = tk.Toplevel(self.root)
        dialog.title('修改任务')
        dialog.geometry('400x300')
        
        tk.Label(dialog, text='任务名称:').pack(pady=5)
        name_entry = tk.Entry(dialog)
        name_entry.insert(0, task['name'])
        name_entry.pack(pady=5)
        
        tk.Label(dialog, text='URL:').pack(pady=5)
        url_entry = tk.Entry(dialog)
        url_entry.insert(0, task['url'])
        url_entry.pack(pady=5)
        
        tk.Label(dialog, text='时间窗口 (格式: HH:MM-HH:MM):').pack(pady=5)
        time_entry = tk.Entry(dialog)
        time_entry.insert(0, ', '.join(task['time_windows']))
        time_entry.pack(pady=5)

        # 添加任务类型选择
        tk.Label(dialog, text='任务类型:').pack(pady=5)
        task_type_var = tk.BooleanVar(value=task['is_one_time'])
        task_type_check = tk.Checkbutton(dialog, text='一次性任务', variable=task_type_var)
        task_type_check.pack(pady=5)

        def save_task():
            name = name_entry.get().strip()
            url = url_entry.get().strip()
            time_windows = [window.strip() for window in time_entry.get().split(',')]
            is_one_time = task_type_var.get()
            
            if not name or not time_windows:
                messagebox.showwarning('警告', '请填写必要信息')
                return
            
            task['name'] = name
            task['url'] = url
            task['time_windows'] = time_windows
            task['is_one_time'] = is_one_time
            
            self.save_tasks()
            self.update_task_list()
            dialog.destroy()
        
        tk.Button(dialog, text='保存', command=save_task).pack(pady=10)
    def setup_scheduler(self):
        # 启动时立即检查一次任务
        self.check_tasks()
        # 每30秒检查一次任务
        schedule.every(300).seconds.do(self.check_tasks)
        schedule.every().day.at('00:00').do(self.reset_all_tasks)
    def run_scheduler(self):
        while True:
            schedule.run_pending()
            time.sleep(1)
    def setup_system_tray(self):
        if not hasattr(self, 'icon') or not self.icon:
            # 创建一个更美观的系统托盘图标
            icon_size = (64, 64)
            image = Image.new('RGBA', icon_size, color=(0,0,0,0))
            draw = Image.new('RGBA', icon_size, color=(0,0,0,0))
            d = ImageDraw.Draw(draw)
            
            # 绘制圆形背景
            d.ellipse([0, 0, 63, 63], fill=self.primary_color)
            
            # 绘制时钟样式图标
            d.ellipse([4, 4, 59, 59], fill='white')
            # 绘制时针
            d.line([32, 32, 32, 15], fill=self.primary_color, width=3)
            # 绘制分针
            d.line([32, 32, 45, 32], fill=self.primary_color, width=3)
            
            image = Image.alpha_composite(image, draw)
            
            menu = pystray.Menu(
                pystray.MenuItem('显示', self.show_window),
                pystray.MenuItem('退出', self.quit_app)
            )
            self.icon = pystray.Icon('task_manager', image, '任务管理器', menu)
            self.icon.on_click = self.on_tray_click
        
    def show_window(self, icon=None, item=None):
        self.root.deiconify()
        self.root.state('normal')
        self.root.lift()
        # 刷新任务列表
        self.update_task_list()
    def on_tray_click(self, icon, button, time):
        if button == pystray.mouse.Button.left:
            self.show_window(icon)
    def quit_app(self, icon=None, item=None):
        if icon:
            icon.stop()
        self.root.quit()
    def run(self):
        # 绑定窗口关闭事件
        self.root.protocol('WM_DELETE_WINDOW', self.on_close)
        
        # 确保窗口在前台显示
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()
        
        # 确保在显示窗口前初始化任务列表
        self.update_task_list()
        
        # 启动系统托盘图标
        threading.Thread(target=self.icon.run, daemon=True).start()
        
        self.root.mainloop()
    def on_close(self):
        self.root.withdraw()
        # 确保系统托盘图标可见
        if not self.icon:
            self.setup_system_tray()
        # 确保系统托盘图标可见
        if not self.icon:
            self.setup_system_tray()
    def update_task_list(self):
        # 清空现有列表
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 批量更新任务列表，减少UI刷新次数
        items_to_insert = []
        for task in self.tasks:
            status = '已完成' if task['completed'] else '未完成'
            task_type = '一次性' if task['is_one_time'] else '常规'
            need_remind = '是' if not task['completed'] and self.is_task_due(task, datetime.now().strftime('%H:%M')) else '否'
            items_to_insert.append((task['name'], status, ', '.join(task['time_windows']), task_type, task['url'], need_remind))
        
        # 一次性插入所有项目
        for item in items_to_insert:
            self.tree.insert('', 'end', values=item)
    def mark_completed(self):
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning('警告', '请先选择一个任务')
            return
        
        task_name = self.tree.item(selected_item[0])['values'][0]
        for task in self.tasks:
            if task['name'] == task_name:
                task['completed'] = True
                break
        
        self.save_tasks()
        self.update_task_list()
    def reset_status(self):
        for task in self.tasks:
            task['completed'] = False
        self.save_tasks()
        self.update_task_list()
    def add_task_dialog(self):
        dialog = tk.Toplevel(self.root)
        dialog.title('添加任务')
        dialog.geometry('400x300')
        
        tk.Label(dialog, text='任务名称:').pack(pady=5)
        name_entry = tk.Entry(dialog)
        name_entry.pack(pady=5)
        
        tk.Label(dialog, text='URL:').pack(pady=5)
        url_entry = tk.Entry(dialog)
        url_entry.pack(pady=5)
        
        tk.Label(dialog, text='时间窗口 (格式: HH:MM-HH:MM):').pack(pady=5)
        time_entry = tk.Entry(dialog)
        time_entry.pack(pady=5)

        # 添加任务类型选择
        tk.Label(dialog, text='任务类型:').pack(pady=5)
        task_type_var = tk.BooleanVar(value=False)
        task_type_check = tk.Checkbutton(dialog, text='一次性任务', variable=task_type_var)
        task_type_check.pack(pady=5)

        def save_task():
            name = name_entry.get().strip()
            url = url_entry.get().strip()
            time_windows = [window.strip() for window in time_entry.get().split(',')]
            is_one_time = task_type_var.get()
            
            if not name or not time_windows:
                messagebox.showwarning('警告', '请填写必要信息')
                return
            
            self.tasks.append({
                'name': name,
                'url': url,
                'time_windows': time_windows,
                'completed': False,
                'is_one_time': is_one_time
            })
            
            self.save_tasks()
            self.update_task_list()
            dialog.destroy()
        
        tk.Button(dialog, text='保存', command=save_task).pack(pady=10)
    def delete_task(self):
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning('警告', '请先选择一个任务')
            return
        
        task_name = self.tree.item(selected_item[0])['values'][0]
        self.tasks = [task for task in self.tasks if task['name'] != task_name]
        
        self.save_tasks()
        self.update_task_list()
    def open_task_url(self, event):
        selected_item = self.tree.selection()
        if not selected_item:
            return
        
        task_name = self.tree.item(selected_item[0])['values'][0]
        task = next((t for t in self.tasks if t['name'] == task_name), None)
        
        if task and task['url']:
            webbrowser.open(task['url'])
    def check_tasks(self):
        # 重新加载任务数据，确保使用最新的任务列表
        self.tasks = self.load_tasks()
        
        current_time = datetime.now().strftime('%H:%M')
        
        for task in self.tasks:
            if not task['completed'] and self.is_task_due(task, current_time):
                self.show_notification(task['name'])
                self.play_alert()
    def is_trading_time(self):
        current_time = datetime.now().strftime('%H:%M')
        return '09:30' <= current_time <= '15:00'
    def is_task_due(self, task, current_time):
        for window in task['time_windows']:
            start_time, end_time = window.split('-')
            # 将时间字符串转换为可比较的格式
            current_time_parts = [int(x) for x in current_time.split(':')]
            start_time_parts = [int(x) for x in start_time.split(':')]
            end_time_parts = [int(x) for x in end_time.split(':')]
            
            current_minutes = current_time_parts[0] * 60 + current_time_parts[1]
            start_minutes = start_time_parts[0] * 60 + start_time_parts[1]
            end_minutes = end_time_parts[0] * 60 + end_time_parts[1]
            
            if start_minutes <= current_minutes <= end_minutes:
                return True
        return False

    def show_notification(self, task_name):
        try:
            # 在程序界面上显示提醒状态并记录日志
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_message = f'[{current_time}] 发送提醒: {task_name}'
            print(log_message)
            
            # 使用Windows原生API发送通知
            wc = win32gui.WNDCLASS()
            wc.lpszClassName = 'TaskNotification'
            wc.lpfnWndProc = {}
            
            try:
                win32gui.RegisterClass(wc)
            except win32gui.error:
                pass  # 类已注册，忽略错误
            
            hwnd = win32gui.CreateWindow(
                'TaskNotification',
                'TaskNotification',
                win32con.WS_OVERLAPPED | win32con.WS_SYSMENU,
                0, 0, 0, 0,
                0, 0, 0, None
            )
            
            # 设置通知图标
            flags = win32gui.NIF_ICON | win32gui.NIF_MESSAGE | win32gui.NIF_TIP | win32gui.NIF_INFO
            nid = (hwnd, 0, flags, win32con.WM_USER + 20,
                   win32gui.LoadIcon(0, win32con.IDI_APPLICATION),
                   '任务提醒',
                   f'请完成任务: {task_name}\n时间: {current_time}',
                   10000,  # 通知显示时间（毫秒）
                   '任务提醒',
                   win32gui.NIIF_INFO
                   )
            
            win32gui.Shell_NotifyIcon(win32gui.NIM_ADD, nid)
            
            # 播放提示音
            self.play_alert()
            
            # 添加延迟以确保通知不会重叠
            time.sleep(18)
            
            # 清理通知图标
            win32gui.Shell_NotifyIcon(win32gui.NIM_DELETE, (hwnd, 0))
            win32gui.DestroyWindow(hwnd)
            
        except Exception as e:
            print(f'发送提醒失败: {str(e)}')
    def play_alert(self):
        winsound.Beep(440, 100)
    def reset_all_tasks(self):
        for task in self.tasks:
            task['completed'] = False
        self.save_tasks()
        self.update_task_list()
    def run(self):
        # 绑定窗口关闭事件
        self.root.protocol('WM_DELETE_WINDOW', self.on_close)
        
        # 确保窗口在前台显示
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()
        
        # 确保在显示窗口前初始化任务列表
        self.update_task_list()
        
        # 启动系统托盘图标
        threading.Thread(target=self.icon.run, daemon=True).start()
        
        self.root.mainloop()

if __name__ == '__main__':
    app = TaskManager()
    app.run()